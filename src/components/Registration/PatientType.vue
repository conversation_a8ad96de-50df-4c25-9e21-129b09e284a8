<template>
    <basic-card :content="cardData" :editable="editable"/>
</template>

<script lang="ts">
import { IonContent, IonHeader, IonItem, IonList, IonTitle, IonToolbar, IonMenu } from "@ionic/vue";
import { defineComponent } from "vue";
import { useRegistrationStore } from "@/stores/RegistrationStore";
import { mapState } from "pinia";
import BasicCard from "../BasicCard.vue";

export default defineComponent({
    name: "Menu",
    components: {
        IonContent,
        IonHeader,
        IonItem,
        IonList,
        IonMenu,
        IonTitle,
        IonToolbar,
        BasicCard,
    },
    data() {
        return {
            cardData: {} as any,
            inputField: "" as any,
            setName: "" as any
        };
    },
    watch: {
        patientType: {
            handler() {
                this.buildCards();
            },
            deep: true,
        },
    },
    computed: {
        ...mapState(useRegistrationStore, ["patientType"]),
    },
    props: {
        editable: {
            default: false as any,
        },
    },
    mounted() {
        this.buildCards();
    },
    methods: {
        buildCards() {
            this.cardData = {
                mainTitle: "Patient Information",
                cards: [
                    {
                        cardTitle: "Patient Type",
                        content: this.patientType,
                    }
                ]
            };
        }
    },
});
</script>

<style scoped>
.demographics_title {
    font-weight: 700;
    font-size: 24px;
}

.gender {
    display: flex;
    justify-content: space-between;
    max-width: 170px;
    padding-top: 10px;
}

.gender_title {
    margin-top: 30px;
}
</style>
