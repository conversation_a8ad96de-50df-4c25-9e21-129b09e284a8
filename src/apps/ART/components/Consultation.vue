<template>
    <ion-card>
        <ion-card-content>
            <ion-accordion-group ref="accordionGroup" :value="expandedAccordions" :multiple="true">
                <ion-accordion style="position: relative !important; overflow: visible !important;"
                    value="lab-investigation">
                    <ion-item slot="header">
                        <div class="accordion-title-container">
                            <div class="section-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 48 48">
                                    <g fill="none" stroke="currentColor" stroke-width="4">
                                        <path stroke-linecap="round"
                                            d="m29.003 18.373l1.105-1.105l.53-.53a5.5 5.5 0 0 0 0-7.778v0a5.5 5.5 0 0 0-7.778 0L9.172 22.648a.143.143 0 0 0 0 .202l6.97 6.97a1 1 0 0 0 1.414 0l3.713-3.713l1.105-1.105" />
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="m28.163 6.485l1.768-1.767a3.5 3.5 0 0 1 4.95 4.95l-1.768 1.767zM10.485 24.163l-4.242 4.243l4.95 4.95l4.242-4.243z" />
                                        <circle cx="26.041" cy="22.042" r="4.5" transform="rotate(45 26.041 22.042)" />
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="m6 20l12.728 12.728M10 44h32M31 22c4 0 8 4 8 10c0 6.4-5.167 9.833-8 12" />
                                    </g>
                                </svg>
                            </div>
                            <ion-label>
                                Lab investigation
                                <span v-if="isLabInvestigationsSaved" class="completed-badge">
                                    <ion-icon size="small" :icon="checkmarkCircle" style="margin-right: 3px"></ion-icon>
                                    Completed
                                </span>
                            </ion-label>
                        </div>
                    </ion-item>
                    <div class="ion-padding" slot="content" style="overflow: visible !important;">
                        <ion-grid class="single-row-grid" style="overflow: visible !important;">
                            <ion-row class="align-items-center">
                                <ion-col size="auto">
                                    <ion-button fill="clear" shape="round" size="small" @click="refreshTestTypes">
                                        <ion-icon :icon="refresh" slot="icon-only"></ion-icon>
                                    </ion-button>
                                </ion-col>
                                <ion-col size="5">
                                    <ion-label class="labl-cls"> Test </ion-label>
                                    <VueMultiselect v-model="selectedTest" teleport
                                        @update:model-value="handleTestChange" :multiple="false" :taggable="false"
                                        :hide-selected="true" :close-on-select="true" openDirection="auto"
                                        tag-placeholder="" placeholder="Select test" selectLabel="" label="name"
                                        :searchable="true" track-by="assigned_id" :options="testOptions"
                                        :disabled="false" />
                                </ion-col>

                                <ion-col size="4">
                                    <ion-label class="labl-cls"> Specimen </ion-label>
                                    <VueMultiselect v-model="selectedSpecimen" :multiple="false" :taggable="false"
                                        :hide-selected="true" :close-on-select="true" openDirection="auto"
                                        tag-placeholder="" placeholder="Select specimen" selectLabel="" label="name"
                                        :searchable="true" track-by="assigned_id" :options="specimenOptions"
                                        :disabled="false" :appendToBody="true" />
                                </ion-col>

                                <ion-col size="auto">
                                    <ion-button class="ion-margin-top" @click="saveLabInvestigation">
                                        <ion-icon :icon="addOutline" slot="start"></ion-icon>
                                        {{ editingInvestigationId ? "Update" : "Add" }}
                                    </ion-button>
                                </ion-col>
                            </ion-row>
                        </ion-grid>

                        <div v-if="labInvestigations.length > 0" class="lab-investigations-list">
                            <div class="table-responsive">
                                <table class="lab-table">
                                    <thead>
                                        <tr>
                                            <th>TEST</th>
                                            <th>SPECIMEN</th>
                                            <th>ACTIONS</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="investigation in labInvestigations" :key="investigation.id">
                                            <td>{{ investigation.test.name }}</td>
                                            <td>{{ investigation.specimen.name }}</td>
                                            <td class="action-buttons">
                                                <ion-button fill="clear" size="small" color="nuetral"
                                                    @click="editLabInvestigation(investigation.id)">
                                                    <ion-icon :icon="createOutline" slot="icon-only"></ion-icon>
                                                </ion-button>
                                                <ion-button fill="clear" size="small" color="danger"
                                                    @click="cancelLabInvestigation(investigation.id)">
                                                    <ion-icon :icon="trashOutline" slot="icon-only"></ion-icon>
                                                </ion-button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="save-button-container">
                            <ion-button expand="block" @click="saveLabInvestigationsStep"> Save and Continue
                            </ion-button>
                        </div>
                    </div>
                </ion-accordion>
                <ion-accordion value="weight-chart">
                    <ion-item slot="header">
                        <div class="accordion-title-container">
                            <div class="section-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24">
                                    <g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="1.5" color="currentColor">
                                        <path
                                            d="M19 15v3.889C19 21.114 18.264 22 15.889 22H8.11C5.886 22 5 21.264 5 18.889V15a7 7 0 1 1 14 0" />
                                        <path
                                            d="M16 15a4 4 0 0 0-8 0m4 2l1-2m3.312-10H7.688c-.884 0-1.326 0-1.715-.152a2 2 0 0 1-.368-.187c-.35-.22-.595-.568-1.085-1.263c-.382-.541-.573-.812-.507-1.033a.5.5 0 0 1 .107-.19C4.28 2 4.624 2 5.313 2h13.376c.689 0 1.033 0 1.192.175q.075.084.107.19c.066.221-.125.492-.507 1.033c-.49.695-.735 1.042-1.085 1.263a2 2 0 0 1-.368.187C17.637 5 17.196 5 16.312 5M14 8V5m-4 3V5" />
                                    </g>
                                </svg>
                            </div>
                            <ion-label>
                                Patient weight chart
                                <span v-if="isWeightChartSaved" class="completed-badge">
                                    <ion-icon size="small" :icon="checkmarkCircle" style="margin-right: 3px"></ion-icon>
                                    Completed
                                </span>
                            </ion-label>
                        </div>
                    </ion-item>
                    <div slot="content" class="ion-padding">
                        <div class="chart-container">
                            <div class="chart-header">
                                <ion-label>Weight trail ({{ chartPeriod }} year period)</ion-label>
                                <ion-button fill="clear" size="small" @click="loadWeightHistory"
                                    :disabled="isLoadingWeightData">
                                    <ion-icon :icon="refresh" slot="icon-only"></ion-icon>
                                </ion-button>
                            </div>

                            <div v-if="isLoadingWeightData" class="loading-container">
                                <ion-spinner name="bubbles" />
                                <p>Loading weight data...</p>
                            </div>

                            <div v-if="!isLoadingWeightData && weightData.length === 0" class="empty-data-container">
                                <p>No weight data available for this patient</p>
                            </div>

                            <div class="chart-canvas-container"
                                :class="{ hidden: isLoadingWeightData || weightData.length === 0 }">
                                <canvas ref="weightChartCanvas" id="weight-chart-canvas"></canvas>
                            </div>

                            <div class="save-button-container" v-if="!isLoadingWeightData">
                                <ion-button expand="block" @click="saveWeightChart"> Continue </ion-button>
                            </div>
                        </div>
                    </div>
                </ion-accordion>

                <ion-accordion v-if="patient.personInformation.gender == 'F'" value="pregnancy-breastfeeding">
                    <ion-item slot="header">
                        <div class="accordion-title-container">
                            <div class="section-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
                                    <g fill="currentColor">
                                        <path
                                            d="M15.751 8.26c.2 0 .386.056.553.148c.398.14.706.485.797.913c.02.12-.08.23-.2.23h-.416h.346a.75.75 0 0 0-.05-.546a.78.78 0 0 0-.7-.434a.77.77 0 0 0-.74.98h.356h-.736c-.15 0-.27-.14-.22-.29q.013-.053.03-.103l-.03-.027a.368.368 0 0 1 .02-.57c.28-.19.64-.3.99-.3" />
                                        <path
                                            d="m15.697 9.55l.004.01h.78l.004-.01a.444.444 0 0 0-.394-.64a.4.4 0 0 0-.168.035a.14.14 0 1 1-.21.184a.43.43 0 0 0-.062.222a.5.5 0 0 0 .046.2m4.594-1.301c.35 0 .71.11.99.3c.19.13.2.41.03.56l-.035.033q.021.057.035.117c.04.15-.08.29-.23.29h-.726l-.004.01h-.78l-.005-.01h-.415c-.12 0-.22-.11-.2-.23c.081-.412.369-.75.75-.898c.176-.106.375-.172.59-.172m.064 1.3h.346q.03-.105.03-.21a.77.77 0 0 0-.77-.77c-.43 0-.78.35-.78.77c0 .08.01.14.03.21h.355a.4.4 0 0 1-.045-.2c0-.083.024-.162.066-.229a.14.14 0 0 0 .254-.08a.14.14 0 0 0-.04-.1a.4.4 0 0 1 .16-.03a.444.444 0 0 1 .395.64m-2.345 2.409c-.45 0-.87-.11-1.23-.3c-.12-.06-.25.08-.18.2c.3.46.82.77 1.41.77s1.11-.3 1.41-.77c.07-.12-.05-.25-.18-.19c-.36.18-.78.29-1.23.29m-.76-1.39l.44-1.42c.1-.31.54-.31.63 0l.44 1.42c.09.29-.12.59-.43.59h-.65c-.3 0-.52-.3-.43-.59m-2.31-2.76a.22.22 0 0 1-.2-.12c-.07-.1-.03-.25.08-.31c.03-.02.78-.44 1.65-.28c.13.02.21.14.19.27c-.02.12-.15.21-.27.18c-.7-.13-1.33.23-1.33.23c-.04.03-.08.03-.12.03m6.02-.03q.06.03.12.03c.08 0 .16-.04.21-.12a.24.24 0 0 0-.112-.33c-.135-.068-.83-.416-1.628-.27a.23.23 0 0 0-.19.27c.02.13.15.21.27.19c.626-.117 1.203.168 1.312.222z" />
                                        <path
                                            d="M24.251 10.63a2.86 2.86 0 0 0-.2-2.82c-.3-.46-.46-.99-.46-1.54v-.01a2.86 2.86 0 0 0-2.86-2.86c-.16 0-.3-.07-.39-.2c-.52-.73-1.37-1.2-2.33-1.2s-1.81.47-2.33 1.2c-.09.12-.24.2-.39.2a2.86 2.86 0 0 0-2.86 2.86v.01c0 .55-.16 1.08-.46 1.54a2.86 2.86 0 0 0-.2 2.82c.06.13.06.27 0 .4a2.79 2.79 0 0 0 2.437 4.01l-1.557 1.67c-.33.35-.53.82-.53 1.34c0 .5.19.96.49 1.3c.09.1.03.27-.1.29a6.385 6.385 0 0 0-5.308 6.168a1.66 1.66 0 0 0-1.198 1.444l-.001.014v.013c-.023.373.077.73.271 1.025a1.66 1.66 0 0 0 .423 1.337q.008.046.019.094l.002.01c.192.793.919 1.255 1.632 1.255h1.042c.036 0 .678.002 1.013-.054a13 13 0 0 0 .88-.162a19 19 0 0 0 2.68-.784h6.405c.96 0 1.74-.78 1.74-1.74V16.05c0-.36-.072-.702-.203-1.016a2.79 2.79 0 0 0 2.333-4.004a.5.5 0 0 1 .01-.4m-1.91-3.116a2 2 0 0 1 .04.386l-.005.125a1.4 1.4 0 0 1 1.133 1.373a1.4 1.4 0 0 1-1.257 1.392c-.227 2.284-2.049 3.79-4.24 3.79c-2.122 0-3.897-1.42-4.215-3.575q-.015-.107-.026-.216a1.399 1.399 0 0 1-.119-2.764l-.01-.265c.007-.635.3-.96.78-1.28c.267-.178.526-.364.73-.6l.005-.005q.11-.127.197-.277l.006-.01q.045-.081.081-.168l.07-.15c.07-.17.3-.2.4-.05a3.2 3.2 0 0 0 .465.545l.006.005c.605.565 1.414.89 2.26.89h2.76c.14 0 .77.061.938.854M8.93 26.74l-.3-.3a.66.66 0 0 1 0-.92c.25-.25.67-.25.92 0l.54.54c.2.2.49.28.77.21c1.24-.31 3.31-.99 4.91-2.38a7 7 0 0 0 1.154-1.275a8 8 0 0 0 .658-1.106c.25-.503.442-1.007.588-1.48v-.009h3.6q-.107.477-.266 1a13 13 0 0 1-.655 1.718a12 12 0 0 1-1.019 1.782a10.7 10.7 0 0 1-1.78 1.99c-3.166 2.74-7.354 3.38-7.78 3.445l-.03.005l-.036.005C9.95 30 9.44 30 9.391 30h-1.04c-.31 0-.59-.2-.66-.49a.67.67 0 0 1 .02-.37l-.057-.021a.645.645 0 0 1-.393-.6c0-.2.09-.38.23-.5a.64.64 0 0 1-.49-.68a.67.67 0 0 1 .67-.6z" />
                                    </g>
                                </svg>
                            </div>
                            <ion-label>
                                Pregnancy or Breast Feeding
                                <span v-if="isPregnancyBreastfeedingSaved" class="completed-badge">
                                    <ion-icon size="small" :icon="checkmarkCircle" style="margin-right: 3px"></ion-icon>
                                    Completed
                                </span>
                            </ion-label>
                        </div>
                    </ion-item>
                    <div slot="content" class="ion-padding">
                        <div>
                            <ion-row>
                                <ion-col>
                                    <ion-label>Pregnant</ion-label>
                                </ion-col>
                                <ion-col>
                                    <div class="radio-group">
                                        <ion-radio-group v-model="pregnancyBreastFeedingData.isPregnant">
                                            <ion-radio slot="end" value="yes">Yes</ion-radio>
                                            <ion-radio slot="end" style="margin-left: 20px" value="no">No</ion-radio>
                                        </ion-radio-group>
                                    </div>
                                </ion-col>
                            </ion-row>

                            <ion-row>
                                <ion-col>
                                    <ion-label>Breastfeeding</ion-label>
                                </ion-col>
                                <ion-col>
                                    <div class="radio-group">
                                        <ion-radio-group v-model="pregnancyBreastFeedingData.isBreastFeeding">
                                            <ion-radio slot="end" value="yes">Yes</ion-radio>
                                            <ion-radio slot="end" style="margin-left: 20px" value="no">No</ion-radio>
                                        </ion-radio-group>
                                    </div>
                                </ion-col>
                            </ion-row>
                            <div class="save-button-container" v-if="!isPregnancyBreastfeedingLoading">
                                <ion-button expand="block" @click="savePregnancyBreastFeedingSection"> Continue
                                </ion-button>
                            </div>
                        </div>
                    </div>
                </ion-accordion>

                <ion-accordion value="contraceptives">
                    <ion-item slot="header">
                        <div class="accordion-title-container">
                            <div class="section-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 48 48">
                                    <path fill="currentColor" fill-rule="evenodd"
                                        d="M16 7.152a2 2 0 0 1 1.347-1.89l3.018-1.043A4 4 0 0 1 21.671 4h4.658a4 4 0 0 1 1.306.22l3.018 1.042A2 2 0 0 1 32 7.152V11h-1.09a1 1 0 0 1-.206.607l-1.51 1.978l5.362 1.756A5 5 0 0 1 38 20.092V39a5 5 0 0 1-5 5H15a5 5 0 0 1-5-5V20.092a5 5 0 0 1 3.444-4.751l5.362-1.756l-1.51-1.978a1 1 0 0 1-.205-.607H16zM19.349 11h9.302l-1.046 1.371a2 2 0 0 0 .967 3.114l5.362 1.756A3 3 0 0 1 36 20.092V39a3 3 0 0 1-3 3H15a3 3 0 0 1-3-3V20.092a3 3 0 0 1 2.066-2.85l5.362-1.757a2 2 0 0 0 .967-3.114zm10.97 21.906A8 8 0 0 0 19.095 21.68l4.113 4.112l-1.414 1.414l-4.113-4.113A8 8 0 0 0 28.906 34.32l-3.113-3.112l1.414-1.414zM24 38c5.523 0 10-4.477 10-10s-4.477-10-10-10s-10 4.477-10 10s4.477 10 10 10m.567-11.99c-.27-.783-.01-1.843.737-2.687c.991-1.122 2.465-1.44 3.292-.71s.694 2.233-.298 3.355c-.648.735-1.504 1.124-2.25 1.101l-.002.005c-.207.347-.302.762-.384 1.176l-.01.045c-.074.38-.18.884-.672 1.394c-.504.523-1.431.707-2.398.872c-.641.11-.973.213-1.135.281c-.133.056-.263.351-.442.756c-.292.662-.713 1.617-1.49 2.302c-.11-.993.203-2.722.38-3.655a1.7 1.7 0 0 1 .606-.753c.473-.338 1.175-.532 2.08-.686c.835-.142 1.06-.321 1.115-.378c.047-.05.086-.11.174-.553l.01-.047c.08-.406.198-1.005.586-1.656a4 4 0 0 1 .1-.161"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                            <ion-label>
                                Contraceptives
                                <span v-if="contraceptivesRef?.isContraceptivesSaved" class="completed-badge">
                                    <ion-icon size="small" :icon="checkmarkCircle" style="margin-right: 3px"></ion-icon>
                                    Completed
                                </span>
                            </ion-label>
                        </div>
                    </ion-item>
                    <div slot="content" class="ion-padding">
                        <ContraceptivesSection :patient-i-d="Number(patientID)" :provider-i-d="providerID"
                            :location-i-d="locationID" ref="contraceptivesRef" @onSuccess="onContraceptivesSuccess"/>
                    </div>
                </ion-accordion>

                <ion-accordion value="contraindications">
                    <ion-item slot="header">
                        <div class="accordion-title-container">
                            <div class="section-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 16 16">
                                    <path fill="currentColor"
                                        d="M3.001 3.999a1.001 1.001 0 0 1 1.836-.555a.5.5 0 1 0 .832-.555A2 2 0 0 0 2 3.999A2 2 0 0 0 4.078 6h3.259a5.5 5.5 0 0 1 3.151-1h-6.41l-.038.001L4.003 5A1 1 0 0 1 3 3.999m.495 3.003h2.76a5.5 5.5 0 0 0-.657 1H3.496a.5.5 0 1 1 0-1M2.501 9h2.706a5.5 5.5 0 0 0-.185 1h-2.52a.5.5 0 0 1 0-1m.995 2h1.526q.048.516.185 1H3.496a.5.5 0 0 1 0-1M7.5 3a.5.5 0 0 0 0 1h6a.5.5 0 1 0 0-1zm7.5 7.5a4.5 4.5 0 1 1-9 0a4.5 4.5 0 0 1 9 0m-4-2a.5.5 0 1 0-1 0V10H8.5a.5.5 0 0 0 0 1H10v1.5a.5.5 0 0 0 1 0V11h1.5a.5.5 0 1 0 0-1H11z" />
                                </svg>
                            </div>
                            <ion-label>
                                Contraindications/side effects
                                <span v-if="isSideEffectsSaved" class="completed-badge">
                                    <ion-icon size="small" :icon="checkmarkCircle" style="margin-right: 3px"></ion-icon>
                                    Completed
                                </span>
                            </ion-label>
                        </div>
                    </ion-item>
                    <div class="ion-padding" slot="content">
                        <div class="side-effects-container">
                            <ion-row>
                                <ion-col>
                                    <ion-label>Has side effects / contraindications</ion-label>
                                </ion-col>
                                <ion-col>
                                    <div class="radio-group">
                                        <ion-radio-group v-model="hasSideEffects">
                                            <ion-radio slot="end" value="yes">Yes</ion-radio>
                                            <ion-radio slot="end" style="margin-left: 20px" value="no">No</ion-radio>
                                        </ion-radio-group>
                                    </div>
                                </ion-col>
                            </ion-row>

                            <div v-if="hasSideEffects === 'yes'" class="side-effects-options">
                                <div v-if="sideEffectsLoading" class="loading-container">
                                    <ion-spinner name="bubbles" />
                                    <p>Loading side effects data...</p>
                                </div>

                                <div class="category-tabs">
                                    <ion-segment v-model="activeCategory" @ionChange="handleCategoryChange">
                                        <ion-segment-button value="contraindication">
                                            <ion-label>Side Effects/Contraindications</ion-label>
                                        </ion-segment-button>
                                        <ion-segment-button value="side_effect">
                                            <ion-label>Others</ion-label>
                                        </ion-segment-button>
                                    </ion-segment>
                                </div>
                                <SideEffectsCheckboxes v-model="sideEffects" :side-effect-options="sideEffectOptions" />


                            </div>
                            <div class="save-button-container">
                                <ion-button expand="block" @click="saveSideEffects"> Save and Continue </ion-button>
                            </div>
                        </div>
                    </div>
                </ion-accordion>

                <ion-accordion value="tb-therapy">
                    <ion-item slot="header">
                        <div class="accordion-title-container">
                            <div class="section-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 48 48">
                                    <g fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                                        <path
                                            d="M35 30.5a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0m-2 0a.5.5 0 1 1-1 0a.5.5 0 0 1 1 0" />
                                        <path
                                            d="M23 20.5V6h2v14.5c0 1.398.564 1.942 1.004 2.199q.073.042.148.078l-.004-.377c-.038-3.528-.112-10.4 5.327-10.4c4.126 0 7.599 7.974 9.335 15.542c.11.263.175.55.188.849c1.232 5.783 1.415 11.14.057 12.27c-3.193 2.653-9.58 1.06-12.773-2.124c-2.578-2.57-2.355-8.578-2.195-12.891v-.002q.016-.393.028-.767a4.3 4.3 0 0 1-1.119-.45a3.8 3.8 0 0 1-.996-.835c-.297.352-.64.626-.996.834c-.4.234-.794.37-1.12.451l.029.769c.16 4.313.383 10.32-2.195 12.891c-3.193 3.185-9.58 4.777-12.773 2.123S9.075 12 16.525 12c5.439 0 5.365 6.872 5.327 10.4v.002l-.004.375q.075-.036.148-.078c.44-.257 1.004-.801 1.004-2.199m15.327 5.36l.038.144a2.5 2.5 0 1 0 1.065 4.817c.27 1.566.449 3.05.526 4.36c.079 1.332.049 2.413-.068 3.188a3.7 3.7 0 0 1-.206.828c-.96.73-2.598 1.022-4.633.628c-2.058-.4-4.076-1.43-5.355-2.704c-.443-.442-.84-1.172-1.137-2.261c-.293-1.076-.45-2.361-.517-3.761c-.066-1.393-.04-2.833.004-4.208l.04-1.126v-.003q.019-.472.034-.927A3.001 3.001 0 0 0 34 24a3 3 0 0 0-5.847-.95q0-.344-.005-.719c-.015-1.648-.034-3.691.411-5.466c.254-1.014.616-1.732 1.05-2.178c.375-.386.912-.687 1.866-.687c.319 0 .777.134 1.395.654c.412.346.846.824 1.289 1.434A2.503 2.503 0 0 0 31 18.5a2.5 2.5 0 0 0 4.917.642c.927 1.962 1.748 4.292 2.41 6.719m.666 2.723l-.08-.366a.5.5 0 1 0 .08.366M16.525 14c.954 0 1.49.3 1.866.687c.434.446.796 1.164 1.05 2.178c.445 1.775.426 3.817.41 5.465l-.004.816c0 .76.033 1.67.069 2.616l.04 1.129c.044 1.375.07 2.815.004 4.208c-.067 1.4-.224 2.685-.517 3.76c-.297 1.09-.694 1.82-1.137 2.262c-1.279 1.275-3.297 2.304-5.355 2.704c-2.035.394-3.673.102-4.633-.628l-.016-.04a3.7 3.7 0 0 1-.19-.788c-.117-.775-.147-1.856-.068-3.188c.157-2.65.727-6.012 1.63-9.32c.905-3.321 2.109-6.46 3.474-8.722c.683-1.133 1.357-1.96 1.982-2.485c.618-.52 1.076-.654 1.395-.654M33.5 19a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1M32 24a1 1 0 1 1-2 0a1 1 0 0 1 2 0" />
                                    </g>
                                </svg>
                            </div>
                            <ion-label>
                                TB Preventive Therapy
                                <span v-if="isTBTherapySaved" class="completed-badge">
                                    <ion-icon size="small" :icon="checkmarkCircle" style="margin-right: 3px"></ion-icon>
                                    Completed
                                </span>
                            </ion-label>
                        </div>
                    </ion-item>
                    <div class="ion-padding" slot="content">
                        <div v-if="submitTBTherapy" class="loading-container">
                            <ion-spinner name="bubbles" />
                            <p>Loading side effects data...</p>
                        </div>
                        <div class="tb-therapy-container">
                            <div class="tb-treatment-section">
                                <label class="form-label required">On TB treatment?</label>
                                <div class="radio-group horizontal-radio-group">
                                    <ion-radio-group v-model="tbTherapyStatus">
                                        <div class="radio-option">
                                            <ion-radio value="Yes"></ion-radio>
                                            <ion-label>Yes</ion-label>
                                        </div>
                                        <div class="radio-option">
                                            <ion-radio value="No"></ion-radio>
                                            <ion-label>No</ion-label>
                                        </div>
                                    </ion-radio-group>
                                </div>
                            </div>

                            <div v-if="tbTherapyStatus.toLowerCase() === 'yes' && tbTherapyStatus.toLowerCase() !== 'unknown'"
                                class="tb-treatment-section">
                                <label class="form-label required">TB Treatment Start Date</label>
                                <ion-row>
                                    <ion-col size="10">
                                        <DatePicker :place_holder="'enter data'" @date-up-dated="() => { }"
                                            :date_prop="tbTreatmentStartDate" />
                                    </ion-col>
                                    <ion-col size="2">
                                        <ion-button fill="clear" :start-icon="calculatorOutline"
                                            @click="showEstimator = true">Estimate</ion-button>
                                    </ion-col>
                                </ion-row>
                                <div v-if="showEstimator" class="estimator-container">
                                    <BasicInputChangeUnits bold="bold"
                                        :input-header="`Estimate number of ${selectedEstimatePeriod.name}`"
                                        v-model:input-value="tbStartEstimate" showAsterisk :placeholder="''" :unitsData="{
                                            isSingleSelect: true,
                                            multiSelectData: tbEstimatePeriods.map((period: EstimatePeriod) => { return { name: period } }),
                                            value: selectedEstimatePeriod,
                                            trackBy: 'name'
                                        }" @update:inputValue="((e: any) => tbStartEstimate = e.target.value)"
                                        @update:units="((e: any) => selectedEstimatePeriod = e)" />
                                </div>
                                <div style="margin-top: 10px">
                                    <label position="stacked" class="form-label required">Treatment Period</label>
                                    <BasicInputField :placeholder="''" :inputValue="tbTreatmentPeriod" :unit="'Months'"
                                        :icon="calendarOutline" :type="'number'" />
                                </div>
                            </div>

                            <div v-else-if="tbTherapyStatus.toLowerCase() === 'no' && tbTherapyStatus.toLowerCase() !== 'unknown'"
                                class="tb-treatment-section">
                                <div class="tb-symptoms-section">
                                    <h4>TB Associated Symptoms</h4>
                                    <TBSymptomsCheckboxes v-model="tbSymptoms" :tb-symptom-options="tbSymptomOptions" />
                                </div>

                                <div class="screening-section">
                                    <ion-row>
                                        <ion-col>
                                            <label class="form-label required">CXR (Chest X-ray) Screening Method
                                                Result</label>
                                        </ion-col>
                                        <ion-col>
                                            <div class="radio-group horizontal-radio-group">
                                                <ion-radio-group v-model="cxrResult">
                                                    <div class="radio-option">
                                                        <ion-radio value="normal"></ion-radio>
                                                        <ion-label>Normal</ion-label>
                                                    </div>
                                                    <div class="radio-option">
                                                        <ion-radio value="abnormal"></ion-radio>
                                                        <ion-label>Abnormal</ion-label>
                                                    </div>
                                                    <div class="radio-option">
                                                        <ion-radio value="unknown"></ion-radio>
                                                        <ion-label>Unknown/Not Done</ion-label>
                                                    </div>
                                                </ion-radio-group>
                                            </div>
                                        </ion-col>
                                    </ion-row>

                                    <ion-row>
                                        <ion-col>
                                            <label class="form-label required">mWRD (Molecular WHO Recommended Rapid
                                                Diagnostic Test) Screening Method Result</label>
                                        </ion-col>
                                        <ion-col>
                                            <div class="radio-group horizontal-radio-group">
                                                <ion-radio-group v-model="mwrdResult">
                                                    <div class="radio-option">
                                                        <ion-radio value="negative"></ion-radio>
                                                        <ion-label>Negative</ion-label>
                                                    </div>
                                                    <div class="radio-option">
                                                        <ion-radio value="positive"></ion-radio>
                                                        <ion-label>Positive</ion-label>
                                                    </div>
                                                    <div class="radio-option">
                                                        <ion-radio value="unknown"></ion-radio>
                                                        <ion-label>Unknown/Not Done</ion-label>
                                                    </div>
                                                </ion-radio-group>
                                            </div>
                                        </ion-col>
                                    </ion-row>
                                </div>

                                <div v-if="showTBStatus(tbScreeningMethodsResults, tbSymptoms)"
                                    class="tb-status-section">
                                    <div>
                                        <label class="form-label">TB Status</label>
                                        <div style="z-index: 99999">
                                            <VueMultiselect v-model="tbStatus" :multiple="false" :taggable="false"
                                                :hide-selected="false" :close-on-select="true" openDirection="bottom"
                                                tag-placeholder="" placeholder="Select TB status" selectLabel=""
                                                label="name" :searchable="true" track-by="assigned_id" :options="tbStatusOptions.map((status) => {
                                                    return { name: status, assigned_id: status };
                                                })
                                                    " :disabled="false" />
                                        </div>
                                    </div>
                                </div>

                                <div class="tb-history-section">
                                    <label class="form-label required">TB preventive therapy (TPT) history</label>
                                    <div>
                                        <VueMultiselect v-model="tptHistory" :multiple="false" :taggable="false"
                                            :hide-selected="true" :close-on-select="true" openDirection="bottom"
                                            tag-placeholder="" placeholder="Select option" selectLabel="" label="name"
                                            :searchable="true" track-by="assigned_id" :options="tptHistoryOptions.map((status) => {
                                                return { name: status, assigned_id: status };
                                            })
                                                " :disabled="false" />
                                    </div>
                                </div>
                            </div>

                            <div class="save-button-container">
                                <ion-button expand="block" @click="saveTBTherapy"> Save and Continue </ion-button>
                            </div>
                        </div>
                    </div>
                </ion-accordion>
            </ion-accordion-group>
        </ion-card-content>
    </ion-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from "vue";
import {
    IonIcon,
    IonButton,
    IonAccordionGroup,
    IonAccordion,
    IonItem,
    IonLabel,
    IonRadioGroup,
    IonRadio,
    IonSpinner,
    IonCardContent,
    IonRow,
    IonCol,
    IonGrid,
    IonCard,
    IonSegment,
    IonSegmentButton,
} from "@ionic/vue";
import VueMultiselect from "vue-multiselect";
import { addOutline, refresh, createOutline, trashOutline, checkmarkCircle, calendarOutline, calculatorOutline } from "ionicons/icons";
import { toastDanger, toastSuccess, toastWarning } from "@/utils/Alerts";
import { LabOrder } from "@/services/lab_order";
import { useWeightChart } from "./consultation/WeightChart";
import { useLabInvestigation } from "./consultation/LabInvestigation";
import { useSideEffects } from "./consultation/SideEffects";
import { useTBSymptoms } from "./consultation/TBSymptoms";
import SideEffectsCheckboxes from "./consultation/SideEffectsCheckboxes.vue";
import TBSymptomsCheckboxes from "./consultation/TBSymptomsCheckboxes.vue";
import { useUserStore } from "@/stores/userStore";
import { EstimatePeriod, ExistingTBTreatment, useTBTherapy } from "./consultation/useTBTherapy";
import DatePicker from "@/components/DatePicker.vue";
import BasicInputField from "@/components/BasicInputField.vue";
import BasicInputChangeUnits from "@/components/BasicInputChangeUnits.vue";
import { AppEncounterService } from "@/services/app_encounter_service";
import { useDemographicsStore } from "@/stores/DemographicStore";
import { Service } from "@/services/service";
import { storeToRefs } from "pinia";
import { nextTick } from "vue";
import { useConsultationValidator } from "./consultation/validator";
import { usePregnancyOrBreastfeeding } from "./consultation/pregnancy";
import ContraceptivesSection from "./consultation/ContraceptivesSection.vue";

const userStore = useUserStore();
const demographicsStore = useDemographicsStore();
const { patient } = storeToRefs(demographicsStore);
const accordionGroup = ref<HTMLIonAccordionGroupElement | null>(null);
const expandedAccordions = ref<string[]>(["lab-investigation"]);
const contraceptivesRef = ref<any>(null);
const hasSideEffects = ref<string>("");
const activeCategory = ref<"side_effect" | "contraindication">("contraindication");
const isSideEffectsSaved = ref<boolean>(false);
const isTBTherapySaved = ref<boolean>(false);
const isLabInvestigationsSaved = ref<boolean>(false);
const isWeightChartSaved = ref<boolean>(false);
const patientID = computed(() => patient.value?.patientID);
const providerID = Service.getUserID() ?? -1;
const locationID = userStore.getfacilityLocation()?.code || "";

const {
    sideEffectOptions,
    sideEffects,
    isLoading: sideEffectsLoading,
    filterByCategory,
    hasSelectedSideEffects,
    saveSideEffectsData,
    encounterId,
} = useSideEffects("side_effect");

const {
    tbTherapyStatus,
    tbTreatmentPeriod,
    tbTreatmentStartDate,
    tbEstimatePeriods,
    selectedEstimatePeriod,
    showEstimator,
    tbStartEstimate,
    tbStatusOptions,
    showTBStatus,
    tptHistoryOptions,
    buildTBTreatmentPayload,
    submitTBTherapy,
} = useTBTherapy();

const { tbSymptomOptions, tbSymptoms } = useTBSymptoms();
const cxrResult = ref<string>("");
const mwrdResult = ref<string>("");
const tbStatus = ref<string>("");
const tptHistory = ref<string>("");

const { pregnancyBreastFeedingData,
    isPregnancyBreastfeedingLoading,
    validationErrors,
    isPregnancyBreastfeedingSaved,
    validatePregnancyBreastFeeding,
    savePregnancyBreastFeeding } = usePregnancyOrBreastfeeding();

const handleCategoryChange = (event: CustomEvent) => {
    const newCategory = event.detail.value;
    activeCategory.value = newCategory;
    filterByCategory(newCategory);
};

const saveSideEffects = async () => {
    if (hasSideEffects.value === "yes" && !hasSelectedSideEffects()) {
        toastDanger("Please select at least one side effect or contraindication");
        return;
    }

    try {
        if (!locationID) {
            toastDanger("Location ID not found");
            return;
        }

        const success = await saveSideEffectsData(patientID.value, providerID, locationID);

        if (success) {
            isSideEffectsSaved.value = true;

            if (accordionGroup.value) {
                expandedAccordions.value = [];
                expandedAccordions.value.push("tb-therapy");
            }
        }
    } catch (error) {
        console.error("Error saving side effects:", error);
        toastDanger("Failed to save side effects");
    }
};

const saveLabInvestigationsStep = async () => {
    if (labInvestigations.value.length === 0) {
        toastDanger("Please add at least one lab investigation");
        return;
    }

    try {

        if (!patientID.value) {
            toastDanger("Patient ID not found");
            return;
        }

        const labOrderInstance = new LabOrder();
        const locationID = userStore.getfacilityLocation()?.code;

        if (!locationID) {
            toastDanger("Location ID not found");
            return;
        }

        const finalOrders = {
            location_id: locationID,
            orders: labInvestigations.value.map((investigation) => ({
                concept_id: investigation.test.id,
                name: investigation.test.name,
                specimen: investigation.specimen.name,
                reason: "Routine",
                specimenConcept: investigation.specimen.id,
            })),
        };

        await labOrderInstance.postActivities(patientID.value, finalOrders);

        isLabInvestigationsSaved.value = true;

        if (accordionGroup.value) {
            expandedAccordions.value = [];
            expandedAccordions.value.push("weight-chart");
        }

        toastSuccess("Lab investigations saved successfully");
    } catch (error) {
        console.error("Error saving lab investigations:", error);
        toastDanger("Failed to save lab investigations");
    }
};

const saveWeightChart = (): void => {
    isWeightChartSaved.value = true;
    if (accordionGroup.value) {
        if (accordionGroup.value) {
            expandedAccordions.value = [];
            expandedAccordions.value.push("pregnancy-breastfeeding");
        }
    }
};

const onContraceptivesSuccess = (): void => {
    if (accordionGroup.value) {
        expandedAccordions.value = [];
        expandedAccordions.value.push("contraindications");
    }
}

const tbScreeningMethodsResults = computed(() => {
    return {
        "Chest X-ray": cxrResult.value,
        "Molecular WHO Recommended Rapid Diagnostic Test": mwrdResult.value,
    };
});

const savePregnancyBreastFeedingSection = async () => {

    if (!patientID.value) {
        toastDanger("Patient ID not found");
        return;
    }

    if (!locationID) {
        toastDanger("Location ID not found");
        return;
    }

    if (!providerID) {
        toastDanger("Provider ID not found");
        return;
    }
    const response = await savePregnancyBreastFeeding(patientID.value, providerID, locationID);
    if (response && accordionGroup.value) {
        if (accordionGroup.value) {
            expandedAccordions.value = [];
            expandedAccordions.value.push("contraceptives");
        }
    }
}

const saveTBTherapy = async () => {
    if (tbTherapyStatus.value === "Unknown") {
        toastWarning("Please select a TB Therapy Status");
        return;
    }

    if (!encounterId.value) {
        toastWarning("Encounter ID not found");
        return;
    }

    if (!patientID.value) {
        toastDanger("Patient ID not found");
        return;
    }

    if (!locationID) {
        toastDanger("Location ID not found");
        return;
    }

    if (!providerID) {
        toastDanger("Provider ID not found");
        return;
    }

    const encounterService = new AppEncounterService(patientID.value, 53, providerID, locationID);
    const existingTBTreatment: ExistingTBTreatment = {
        history: tptHistory.value,
        status: tbStatus.value,
        methods: tbScreeningMethodsResults.value,
        symptoms: tbSymptoms.value,
        encounterService: encounterService,
    };

    try {
        submitTBTherapy.value = true;
        const payload = await buildTBTreatmentPayload(patientID.value, 53, providerID, locationID, existingTBTreatment);

        if (!payload || payload.some((item) => !item)) {
            toastDanger("Failed to build TB therapy payload");
            throw new Error("Failed to build TB therapy payload");
        }

        const response = await AppEncounterService.saveObsArray(Number(encounterId.value), payload);
        if (response) {
            toastSuccess("TB therapy data saved successfully");
            isTBTherapySaved.value = true;

            if (accordionGroup.value) {
                expandedAccordions.value = [];
            }
        }
    } catch (error) {
        console.error("Error saving TB therapy data:", error);
        toastDanger("Failed to save TB therapy data");
    } finally {
        submitTBTherapy.value = false;
    }
};

const {
    testOptions,
    specimenOptions,
    selectedTest,
    selectedSpecimen,
    labInvestigations,
    editingInvestigationId,
    loadTestTypes,
    handleTestChange,
    refreshTestTypes,
    saveLabInvestigation,
    editLabInvestigation,
    cancelLabInvestigation,
} = useLabInvestigation();

const { chartPeriod, weightChartCanvas, isLoadingWeightData, weightData, loadWeightHistory, initializeWeightChart, cleanupChart } = useWeightChart();

const { validate } = useConsultationValidator(
    isLabInvestigationsSaved,
    isWeightChartSaved,
    isSideEffectsSaved,
    isTBTherapySaved,
);
/**
 * This function will be consumed by the ARTVisit workflow Wizard
 * TODO: add your submission logic here
 */
async function onSubmit(): Promise<boolean> {
    const data = {
        "lab-investigation": labInvestigations.value,
        "weight-chart": weightChartCanvas.value,
        "side-effects": sideEffects.value,
        "tb-therapy": tbTherapyStatus.value,
    }
    return (await validate());
}

onMounted(async (): Promise<void> => {
    await loadTestTypes();
    await loadWeightHistory();

    setTimeout(() => {
        if (weightData.value.length > 0) {
            initializeWeightChart();
        }
    }, 500);
});

onUnmounted(() => {
    cleanupChart();
});

defineExpose({
    onSubmit
});

const adjustMultiselectWidth = () => {
    const multiselectElements: any = document.querySelectorAll('.multiselect');
    multiselectElements.forEach((multiselect: any) => {
        const contentWrapper = multiselect.querySelector('.multiselect__content-wrapper');
        if (contentWrapper) {
            const parentWidth = multiselect.clientWidth || '320';
            contentWrapper.style.width = parentWidth + 'px';
        }
    });
}

onMounted(() => {
    nextTick(() => {
        // Await for 100 miliseconds for component to render
        setTimeout(() => {
            adjustMultiselectWidth();
        }, 1000);
    });
});

</script>

<style lang="css" scoped>
ion-card {
    --background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

ion-item {
    --background: white;
}

ion-accordion {
    --background: white;
}

ion-accordion-group {
    --background: white;
}

ion-card-content {
    --background: white;
    background-color: white;
}

ion-card-title {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 4px;
}

ion-row {
    display: flex;
    flex-wrap: nowrap;
    gap: 1rem;
    align-items: center;
    width: 100%;
}

ion-col {
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
}

ion-card-header {
    border-bottom: 1px dotted #969696;
    margin-bottom: 10px;
}

/* Solution 1: Adjust accordion overflow and z-index */
.ion-accordion {
    overflow: visible !important;
}

.ion-accordion-content {
    overflow: visible !important;
    z-index: 1000;
}

/* Solution 2: Specific VueMultiselect container fixes */
.multiselect {
    z-index: 9999 !important;
}

.multiselect__content-wrapper {
    z-index: 9999 !important;
    position: relative !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    max-height: 300px !important;
    overflow-y: auto !important;
    border: 10px solid red !important;
    border-top: none !important;
    background: white !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.multiselect--active {
    z-index: 10000 !important;
}

.multiselect__option {
    padding: 12px 16px !important;
    border-bottom: 1px solid #f0f0f0 !important;
}

.multiselect__option--highlight {
    background: #007bff !important;
    color: white !important;
}

/* Solution 3: Card and grid container fixes */
.ion-card {
    overflow: visible !important;
}

.single-row-grid {
    overflow: visible !important;
}


ion-accordion[slot="content"] {
    overflow: visible !important;
}

.bold {
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #00190e;
}

.single-row-grid {
    width: 100%;
    position: relative;
}

.single-row-grid ion-row {
    flex-wrap: nowrap !important;
}

.single-row-grid ion-col[size="auto"] {
    flex: 0 0 auto;
    width: auto;
}

.align-items-center {
    align-items: center !important;
}

.labl-cls {
    margin: 10px;
    margin-left: 0px;
    margin-top: 0px;
    margin-bottom: 10px;
    color: grey;
    font-size: 17px;
    font-weight: 600;
}

.select-container {
    position: relative;
    border-radius: 4px;
    overflow: visible;
    display: flex;
    align-items: center;
    padding: 0;
    height: 48px;
    z-index: 1;
}

ion-col,
ion-row,
.multiselect {
    overflow: visible !important;
    position: relative;
}

.form-actions {
    display: flex;
    align-items: center;
    margin-top: 24px;
}

.chart-container {
    min-height: 400px;
    width: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.chart-container ion-label {
    font-size: 17px;
    margin-top: 0;
    margin-bottom: 0;
    font-weight: 500;
}

.loading-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid #3498db;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.chart-canvas-container {
    height: 300px;
    width: 100%;
    margin-bottom: 20px;
}

.chart-canvas-container canvas {
    width: 100% !important;
    height: 100% !important;
}

.hidden {
    display: none;
}

.empty-data-container {
    position: relative;
    margin: 40px auto;
    text-align: center;
    color: #666;
    font-size: 16px;
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    width: 80%;
    max-width: 400px;
}

.error-container,
.validation-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background-color: rgba(255, 0, 0, 0.05);
    border-radius: 8px;
    margin-bottom: 20px;
}

.error-container ion-icon,
.validation-error ion-icon {
    font-size: 24px;
    margin-bottom: 10px;
}

.error-container p,
.validation-error p {
    color: var(--ion-color-danger);
    font-weight: 500;
    text-align: center;
    margin: 0;
}

.save-button-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    position: relative;
    z-index: 10;
}

.completed-badge {
    display: inline-flex;
    background-color: var(--ion-color-primary);
    color: white;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    margin-left: 10px;
    font-weight: 500;
    justify-content: space-around;
    align-content: center;
    align-items: center;
}

.side-effects-row {
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.radio-group {
    display: flex;
    gap: 1rem;
}

.horizontal-radio-group {
    display: flex;
    flex-direction: row;
    gap: 2rem;
    margin-top: 0.5rem;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 10px;
}

.tb-treatment-section {
    margin-bottom: 1.5rem;
}

.radio-item {
    --min-height: 35px;
    --padding-start: 0;
    --inner-padding-end: 0;
    --background: transparent;
    --border-color: transparent;
    --inner-border-width: 0;
}

.side-effects-options {
    margin-top: 1rem;
}

.category-tabs {
    margin-bottom: 1.5rem;
}

.side-effects-section {
    margin-top: 2rem;
}

.side-effects-section h4 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-weight: 500;
    font-size: 1.1rem;
}

.medication-options {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.medication-option {
    background-color: rgba(0, 100, 2, 0.034);
    border-radius: 8px;
    padding: 12px;
}

ion-segment {
    --background: rgba(0, 100, 2, 0.034);
}

.medication-option h5 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    font-weight: 500;
    font-size: 1rem;
    color: var(--ion-color-primary);
}

.tb-treatment-row {
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.tb-symptoms-section {
    margin-bottom: 2rem;
}

.tb-symptoms-section h4 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-weight: 500;
    font-size: 1.1rem;
}

.screening-section {
    margin-bottom: 2rem;
}

.screening-section .form-row {
    margin-bottom: 1.5rem;
}

.tb-status-section,
.tb-history-section {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-label {
    font-weight: 500;
    margin-right: 1rem;
    min-width: 150px;
}

.form-label.required::after {
    content: " *";
    color: #d32f2f;
}

.accordion-title-container {
    display: flex;
    align-items: center;
    width: 100%;
}

.section-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    margin-bottom: 5px;
    font-size: 20px;
    color: var(--ion-color-primary);
    background-color: rgba(0, 100, 2, 0.147);
    border-radius: 5px;
    padding: 10px;
    width: 45px;
    height: 45px;
}

.section-icon ion-icon {
    font-size: 25px;
    color: var(--ion-color-primary);
}

.section-icon svg {
    color: var(--ion-color-primary);
}

:deep(.multiselect__input) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

:deep(.multiselect__placeholder) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

:deep(.multiselect__single) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

:deep(.multiselect__option) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

:deep(.multiselect__content) {
    display: block !important;
}

:deep(.multiselect__element) {
    display: block !important;
}

ion-item[slot="header"] {
    font-size: 20px;
    padding-top: 25px;
    font-weight: 600;
    --inner-padding-end: 0;
    --background: white;
    background-color: white;
}

ion-accordion[disabled] ion-item[slot="header"] {
    opacity: 0.7;
}

ion-accordion[disabled] .section-icon {
    background-color: rgba(0, 100, 2, 0.05);
}

.lab-investigations-list {
    margin-top: 20px;
    border-top: 1px solid #eee;
    padding-top: 15px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.header-row {
    font-weight: 600;
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
    margin-bottom: 8px;
}

.investigation-row {
    border-bottom: 1px solid #f5f5f5;
    padding: 10px 0;
    align-items: center;
}

.investigation-row:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.action-buttons {
    display: flex;
    justify-content: flex-start;
    gap: 5px;
}

.table-responsive {
    width: 100%;
    overflow-x: auto;
    margin-bottom: 15px;
}

.lab-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 0;
}

.lab-table thead th {
    font-weight: 600;
    padding: 10px;
    border-bottom: 1px solid #dee2e6;
    text-align: left;
}

.lab-table tbody td {
    padding: 10px;
    vertical-align: middle;
    border-bottom: 1px solid #f5f5f5;
}

.lab-table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}
</style>
