import { AppEncounterService } from '@/services/app_encounter_service';
import { ConsultationService } from '@/apps/ART/services/consultation_service';
import { ref, reactive, computed } from "vue";
import { toastSuccess, toastDanger } from '@/utils/Alerts';

export interface ContraceptiveOption {
    label: string;
    value: string;
    isChecked: boolean;
    disabled: boolean;
}

export const useContraceptives = () => {
    const contraceptiveData = reactive({
        selectedMethods: [] as string[],
        availableMethods: [] as ContraceptiveOption[],
    });

    const isContraceptivesLoading = ref<boolean>(false);
    const isContraceptivesSaved = ref<boolean>(false);
    const validationErrors = ref<string[]>([]);

    const initializeContraceptiveMethods = () => {
        const consultationService = new ConsultationService(0, 0);
        const methods = consultationService.getFamilyPlanningMethods();
        
        contraceptiveData.availableMethods = methods.map(method => ({
            label: method,
            value: method,
            isChecked: false,
            disabled: false,
        }));
    };

    const validateContraceptives = (): boolean => {
        validationErrors.value = [];
        if (contraceptiveData.selectedMethods.length === 0) {
            validationErrors.value.push("At least one contraceptive method must be selected.");
        }
        return validationErrors.value.length === 0;
    };

    const toggleMethod = (method: string) => {
        const consultationService = new ConsultationService(0, 0);
        
        if (method === "NONE") {
            contraceptiveData.selectedMethods = ["NONE"];
            contraceptiveData.availableMethods = contraceptiveData.availableMethods.map(opt => ({
                ...opt,
                isChecked: opt.value === "NONE",
                disabled: false,
            }));
        } else {
            if (contraceptiveData.selectedMethods.includes("NONE")) {
                contraceptiveData.selectedMethods = [];
            }

            const index = contraceptiveData.selectedMethods.indexOf(method);
            if (index > -1) {
                contraceptiveData.selectedMethods.splice(index, 1);
            } else {
                contraceptiveData.selectedMethods.push(method);
            }

            if (contraceptiveData.selectedMethods.length > 0) {
                const primaryMethod = contraceptiveData.selectedMethods[0];
                const updatedMethods = consultationService.familyPlanningMethods(
                    primaryMethod,
                    contraceptiveData.availableMethods
                );
                contraceptiveData.availableMethods = updatedMethods;
            } else {
                contraceptiveData.availableMethods = contraceptiveData.availableMethods.map(opt => ({
                    ...opt,
                    disabled: false,
                }));
            }

            contraceptiveData.availableMethods = contraceptiveData.availableMethods.map(opt => ({
                ...opt,
                isChecked: contraceptiveData.selectedMethods.includes(opt.value),
            }));
        }
    };

    const buildContraceptiveObs = async (
        patientID: number,
        providerID: number,
        locationID: string
    ): Promise<any[]> => {
        const encounterService = new AppEncounterService(patientID, 53, providerID, locationID);
        const observations: any[] = [];

        for (const method of contraceptiveData.selectedMethods) {
            const obs = await encounterService.buildValueCoded(
                'Method of family planning',
                method
            );
            observations.push(obs);
        }

        return observations;
    };

    const saveContraceptives = async (
        patientID: number,
        providerID: number,
        locationID: string
    ): Promise<boolean> => {
        if (!validateContraceptives()) {
            console.error("Validation failed:", validationErrors.value);
            toastDanger("Please select at least one contraceptive method");
            return false;
        }

        isContraceptivesLoading.value = true;
        try {
            const observations = await buildContraceptiveObs(patientID, providerID, locationID);
            
            if (observations.length > 0) {
                const encounterService = new AppEncounterService(patientID, 53, providerID, locationID);
                await encounterService.saveObservationList(observations);
                console.info("Contraceptive methods saved:", observations);
                isContraceptivesSaved.value = true;
                toastSuccess("Contraceptive methods saved successfully");
                return true;
            } else {
                console.warn("No contraceptive observations to save");
                toastDanger("No contraceptive methods to save");
                return false;
            }
        } catch (error) {
            console.error("Error saving contraceptive data:", error);
            toastDanger("Failed to save contraceptive methods");
            return false;
        } finally {
            isContraceptivesLoading.value = false;
        }
    };

    const hasSelectedMethods = computed(() => contraceptiveData.selectedMethods.length > 0);
    const selectedMethodsText = computed(() => {
        if (contraceptiveData.selectedMethods.length === 0) return "None selected";
        if (contraceptiveData.selectedMethods.includes("NONE")) return "None";
        return contraceptiveData.selectedMethods.join(", ");
    });

    initializeContraceptiveMethods();

    return {
        contraceptiveData,
        isContraceptivesLoading,
        isContraceptivesSaved,
        validationErrors,
        hasSelectedMethods,
        selectedMethodsText,
        validateContraceptives,
        toggleMethod,
        saveContraceptives,
        initializeContraceptiveMethods,
    };
};
