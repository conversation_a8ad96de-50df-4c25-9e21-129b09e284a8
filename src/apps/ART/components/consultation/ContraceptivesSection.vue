<template>
  <div class="contraceptives-section">
    <div v-if="hasSelectedMethods && !contraceptiveData.selectedMethods.includes('NONE')" class="selected-methods">
      <div class="method-tag" v-for="method in contraceptiveData.selectedMethods" :key="method">
        {{ method }}
        <ion-icon :icon="close" @click="toggleMethod(method)" class="remove-icon"></ion-icon>
      </div>
    </div>

    <div v-if="contraceptiveData.selectedMethods.includes('NONE')" class="selected-methods">
      <div class="method-tag none-tag">
        NONE
        <ion-icon :icon="close" @click="toggleMethod('NONE')" class="remove-icon"></ion-icon>
      </div>
    </div>

    <div class="methods-list">
      <div 
        v-for="method in contraceptiveData.availableMethods" 
        :key="method.value"
        class="method-item"
        :class="{ 'disabled': method.disabled }"
      >
        <ion-checkbox
          :checked="method.isChecked"
          :disabled="method.disabled"
          @ion-change="toggleMethod(method.value)"
          labelPlacement="end"
        >
          {{ method.label }}
        </ion-checkbox>
      </div>
    </div>

    <div v-if="validationErrors.length > 0" class="validation-errors">
      <div v-for="error in validationErrors" :key="error" class="error-message">
        {{ error }}
      </div>
    </div>

    <div class="action-buttons">
      <ion-button 
        color="medium" 
        fill="outline" 
        @click="clearSelection"
        :disabled="contraceptiveData.selectedMethods.length == 0"
      >
        Clear
      </ion-button>
      
      <ion-button 
        color="primary" 
        @click="handleSave"
        :disabled="isContraceptivesLoading || !hasSelectedMethods"
        :loading="true"
      >
        Save
      </ion-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  IonCheckbox, 
  IonButton, 
  IonIcon, 
} from '@ionic/vue';
import { close } from 'ionicons/icons';
import { useContraceptives } from './contraceptives';
import { computed } from 'vue';
import { useUserStore } from '@/stores/userStore';
import { useDemographicsStore } from '@/stores/DemographicStore';
import { storeToRefs } from 'pinia';
import { Service } from '@/services/service';

interface Props {
  patientID?: number;
  providerID?: number;
  locationID?: string;
}

const props = withDefaults(defineProps<Props>(), {
  patientID: 0,
  providerID: -1,
  locationID: '',
});

const emits = defineEmits(['onSuccess'])

const userStore = useUserStore();
const demographicsStore = useDemographicsStore();
const { patient } = storeToRefs(demographicsStore);

const computedPatientID = computed(() => props.patientID || patient.value?.patientID || 0);
const computedProviderID = computed(() => props.providerID !== -1 ? props.providerID : Service.getUserID() ?? -1);
const computedLocationID = computed(() => props.locationID || userStore.getfacilityLocation()?.code || '');

const {
  contraceptiveData,
  isContraceptivesLoading,
  isContraceptivesSaved,
  validationErrors,
  hasSelectedMethods,
  selectedMethodsText,
  validateContraceptives,
  toggleMethod,
  saveContraceptives,
  initializeContraceptiveMethods,
} = useContraceptives();

const clearSelection = () => {
  contraceptiveData.selectedMethods = [];
  contraceptiveData.availableMethods = contraceptiveData.availableMethods.map(method => ({
    ...method,
    isChecked: false,
    disabled: false,
  }));
};

const handleSave = async (): Promise<void> => {
  const success = await saveContraceptives(
    computedPatientID.value,
    computedProviderID.value,
    computedLocationID.value
  );
  
  if (success) {
    emits("onSuccess", true)
  }
};

defineExpose({
  saveContraceptives: handleSave,
  validateContraceptives,
  isContraceptivesSaved,
});
</script>

<style scoped>
.contraceptives-section {
  padding: 16px;
}

.section-header h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--ion-color-dark);
}

.selected-methods {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.method-tag {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: var(--ion-color-primary);
  color: white;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.method-tag.none-tag {
  background-color: var(--ion-color-primary);
}

.remove-icon {
  cursor: pointer;
  font-size: 16px;
}

.methods-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.method-item {
  padding: 12px;
  border: 1px solid var(--ion-color-light);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.method-item:not(.disabled):hover {
  background-color: var(--ion-color-light);
}

.method-item.disabled {
  opacity: 0.5;
  background-color: var(--ion-color-light-shade);
}

.validation-errors {
  margin-bottom: 16px;
}

.error-message {
  color: var(--ion-color-danger);
  font-size: 14px;
  margin-bottom: 4px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.action-buttons ion-button {
  min-width: 100px;
}
</style>
