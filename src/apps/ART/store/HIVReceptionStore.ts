import { defineStore } from "pinia";
import { icons } from "@/utils/svg";
import _, { cloneDeep } from "lodash";

const initialReception = [
    {
        classDash: "dashed_bottom_border",
        displayStyle: "inline",
        radioBtnContent: {
            header: {
                class: "bold",
                title: "Patient present?*",
                selectedValue: "",
                name: "Patient present",
                displayNext: "Yes",
                alertsErrorMassage: "",
                validationFunctionName: "required",
            },
            data: [
                {
                    labelPlacement:"end",
                    value: "Yes",
                    name: "Yes",
                    colSize: "7",
                },
                {
                    name: "No",
                    value: "No",
                    colSize: "7",
                    labelPlacement:"end",

                },
            ],
        },
    },
    {
        classDash: "dashed_bottom_border",
        radioBtnContent: {
            header: {
                class: "bold",
                title: "Guardian present?*",
                selectedValue: "",
                name: "Guardian present",
                alertsErrorMassage: "",
                validationFunctionName: "required",

            },
            data: [
                {
                    labelPlacement:"end",
                    value: "Yes",
                    name: "Yes",
                    colSize: "7",
                },
                {
                    labelPlacement:"end",
                    name: "No",
                    value: "No",
                    colSize: "7",
                },
            ],
        },
    },

] as any;

const initialARTNumber=[
    {
        classDash: "dashed_bottom_border",
        radioBtnContent: {
            header: {
                class: "bold",
                title: "Capture ARV number*",
                selectedValue: "",
                name: "Capture ARV number",
                displayNext: "Yes",
                validationFunctionName: "required",
            },
            data: [
                {
                    labelPlacement:"end",
                    value: "Yes",
                    name: "Yes",
                    colSize: "7",
                },
                {
                    labelPlacement:"end",
                    name: "No",
                    value: "No",
                    colSize: "7",
                },
            ],
        },
    },
    {
        sideColSize: 0,
        childName: "Capture ARV number",
        selectedData: [],
        isFinishBtn: false,
        data: {
            rowData: [
                {
                    colData: [
                        {
                            displayNone: true,
                            iconRight: icons.editStarts,
                            leftText: "MPC-ARV",
                            value: "",
                            name: "Art number",
                            placeholder: "__-__-__-__",
                            eventType: "input",
                            alertsErrorMassage: "",
                            validationFunctionName: "isNumber",
                        },
                    ],
                },
            ],
        },
    },
] as any

export const useHIVReceptionStore = defineStore("HIVReceptionStore", {
    state: () => ({
        HIVReception: [...initialReception] as any,
        ArtNumber: [...initialARTNumber] as any,

    }),
    actions: {
        setReception(data: any) {
            this.HIVReception = data;
        },
        setArtNumber(data: any) {
            this.ArtNumber = data;
        },
        getInitial() {
            const data =_.cloneDeep(initialReception);
            return [...data];
        },
        getInitialArtNumber() {
            const data =_.cloneDeep(initialARTNumber);
            return [...data];
        }
    },
    // persist:true,
});
